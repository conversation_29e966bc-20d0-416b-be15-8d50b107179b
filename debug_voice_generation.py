#!/usr/bin/env python3
"""
Debug voice generation to find the exact issue
"""

import torch
import torch.nn as nn
import numpy as np
import soundfile as sf
from pathlib import Path

def debug_model_loading():
    """Debug the model loading process"""
    print("🔍 DEBUGGING MODEL LOADING")
    print("=" * 50)
    
    model_path = "bd_bengali_tts_finetuned_final.pt"
    
    if not Path(model_path).exists():
        print(f"❌ Model file not found: {model_path}")
        return None
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        print(f"✅ Checkpoint loaded successfully")
        print(f"📊 Checkpoint keys: {list(checkpoint.keys())}")
        
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            print(f"📊 Model state dict keys: {list(state_dict.keys())[:10]}...")  # First 10 keys
            print(f"📊 Total parameters in state dict: {len(state_dict)}")
            
            # Check parameter shapes
            for name, param in list(state_dict.items())[:5]:
                print(f"   • {name}: {param.shape}")
        
        return checkpoint
        
    except Exception as e:
        print(f"❌ Error loading checkpoint: {e}")
        return None

def debug_model_forward():
    """Debug the model forward pass"""
    print("\n🔍 DEBUGGING MODEL FORWARD PASS")
    print("=" * 50)
    
    # Create a simple test model
    class TestBDTTSModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.text_encoder = nn.Sequential(
                nn.Embedding(256, 256),
                nn.LSTM(256, 256, batch_first=True)
            )
            self.mel_generator = nn.Linear(256, 80)
        
        def forward(self, text_ids):
            print(f"   📥 Input shape: {text_ids.shape}")
            
            # Text encoding
            text_features, _ = self.text_encoder(text_ids)
            print(f"   🔄 Text features shape: {text_features.shape}")
            
            # Generate mel
            mel_output = self.mel_generator(text_features)
            print(f"   📊 Mel output shape: {mel_output.shape}")
            
            # Transpose for proper mel format [batch, mel_dims, time]
            mel_output = mel_output.transpose(1, 2)
            print(f"   📊 Final mel shape: {mel_output.shape}")
            
            return mel_output
    
    try:
        model = TestBDTTSModel()
        model.eval()
        
        # Test input
        text = "আমি বাংলাদেশের মানুষ।"
        text_ids = torch.tensor([[ord(c) % 256 for c in text[:20]]], dtype=torch.long)
        print(f"📝 Test text: {text}")
        print(f"📝 Text IDs shape: {text_ids.shape}")
        
        with torch.no_grad():
            mel_output = model(text_ids)
            print(f"✅ Model forward pass successful!")
            return mel_output.cpu().numpy()
            
    except Exception as e:
        print(f"❌ Model forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_mel_to_audio(mel_spec):
    """Debug mel-spectrogram to audio conversion"""
    print("\n🔍 DEBUGGING MEL-TO-AUDIO CONVERSION")
    print("=" * 50)
    
    if mel_spec is None:
        print("❌ No mel-spectrogram to convert")
        return None
    
    print(f"📊 Input mel shape: {mel_spec.shape}")
    
    try:
        # Simple mel-to-audio conversion
        if len(mel_spec.shape) == 3:
            mel_spec = mel_spec[0]  # Remove batch dimension
            print(f"📊 After batch removal: {mel_spec.shape}")
        
        # Ensure correct orientation [mel_dims, time]
        if mel_spec.shape[0] != 80:
            mel_spec = mel_spec.T
            print(f"📊 After transpose: {mel_spec.shape}")
        
        # Calculate duration
        time_steps = mel_spec.shape[1]
        duration = time_steps * 256 / 22050  # hop_length = 256
        print(f"⏱️ Duration: {duration:.2f} seconds")
        
        # Generate time axis
        sample_rate = 22050
        t = np.linspace(0, duration, int(sample_rate * duration))
        print(f"📊 Time axis: {len(t)} samples")
        
        # Extract fundamental frequency from mel features
        mel_mean = np.mean(mel_spec, axis=1)  # Average over time
        f0_base = 120 + (mel_mean[0] - np.min(mel_mean)) / (np.max(mel_mean) - np.min(mel_mean) + 1e-8) * 80
        print(f"🎵 Base frequency: {f0_base:.1f} Hz")
        
        # Generate audio with multiple harmonics
        audio = np.zeros_like(t)
        
        # Fundamental frequency
        audio += 0.5 * np.sin(2 * np.pi * f0_base * t)
        
        # Add harmonics
        for harmonic in range(2, 6):
            amplitude = 0.3 / harmonic
            freq = f0_base * harmonic
            audio += amplitude * np.sin(2 * np.pi * freq * t)
        
        # Apply envelope
        envelope = np.exp(-t * 0.5) * (1 - np.exp(-t * 5))
        audio *= envelope
        
        # Normalize
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio)) * 0.7
        
        print(f"✅ Audio generated: {len(audio)} samples, {len(audio)/sample_rate:.2f}s")
        print(f"📊 Audio range: [{np.min(audio):.3f}, {np.max(audio):.3f}]")
        
        return audio, sample_rate
        
    except Exception as e:
        print(f"❌ Mel-to-audio conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def debug_audio_saving(audio, sample_rate):
    """Debug audio file saving"""
    print("\n🔍 DEBUGGING AUDIO SAVING")
    print("=" * 50)
    
    if audio is None:
        print("❌ No audio to save")
        return False
    
    try:
        filename = "debug_test_audio.wav"
        sf.write(filename, audio, sample_rate)
        
        # Verify file was created
        if Path(filename).exists():
            file_size = Path(filename).stat().st_size
            print(f"✅ Audio saved: {filename}")
            print(f"📁 File size: {file_size} bytes")
            
            # Try to read it back
            audio_read, sr_read = sf.read(filename)
            print(f"✅ Audio read back: {len(audio_read)} samples at {sr_read} Hz")
            
            return True
        else:
            print(f"❌ File not created: {filename}")
            return False
            
    except Exception as e:
        print(f"❌ Audio saving failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_librosa_vocoder():
    """Test if librosa vocoder works"""
    print("\n🔍 TESTING LIBROSA VOCODER")
    print("=" * 50)
    
    try:
        import librosa
        print("✅ Librosa available")
        
        # Create test mel-spectrogram
        mel_spec = np.random.randn(80, 100) * 0.5 - 2.0  # Log mel-spectrogram
        print(f"📊 Test mel shape: {mel_spec.shape}")
        
        # Convert to linear spectrogram
        mel_basis = librosa.filters.mel(sr=22050, n_fft=1024, n_mels=80)
        linear_spec = np.dot(mel_basis.T, np.exp(mel_spec))
        print(f"📊 Linear spec shape: {linear_spec.shape}")
        
        # Griffin-Lim reconstruction
        audio = librosa.griffinlim(linear_spec, n_iter=32, hop_length=256)
        print(f"✅ Griffin-Lim successful: {len(audio)} samples")
        
        # Save test audio
        sf.write("debug_griffinlim_test.wav", audio, 22050)
        print(f"✅ Griffin-Lim test audio saved")
        
        return True
        
    except ImportError:
        print("❌ Librosa not available")
        return False
    except Exception as e:
        print(f"❌ Librosa vocoder failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all debug tests"""
    print("🔍 COMPREHENSIVE VOICE GENERATION DEBUG")
    print("=" * 60)
    
    # Test 1: Model loading
    checkpoint = debug_model_loading()
    
    # Test 2: Model forward pass
    mel_output = debug_model_forward()
    
    # Test 3: Mel-to-audio conversion
    audio, sample_rate = debug_mel_to_audio(mel_output)
    
    # Test 4: Audio saving
    audio_saved = debug_audio_saving(audio, sample_rate)
    
    # Test 5: Librosa vocoder
    librosa_works = test_librosa_vocoder()
    
    # Summary
    print("\n" + "=" * 60)
    print("🔍 DEBUG SUMMARY")
    print("=" * 60)
    print(f"Model Loading: {'✅' if checkpoint else '❌'}")
    print(f"Model Forward: {'✅' if mel_output is not None else '❌'}")
    print(f"Mel-to-Audio: {'✅' if audio is not None else '❌'}")
    print(f"Audio Saving: {'✅' if audio_saved else '❌'}")
    print(f"Librosa Vocoder: {'✅' if librosa_works else '❌'}")
    
    if audio_saved:
        print(f"\n🎵 Test audio files created:")
        print(f"   • debug_test_audio.wav")
        if librosa_works:
            print(f"   • debug_griffinlim_test.wav")
        print(f"\n🔊 Try playing these files to test audio generation!")

if __name__ == "__main__":
    main()
