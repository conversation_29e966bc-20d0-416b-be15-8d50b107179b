#!/usr/bin/env python3
"""
Task 2: Dataset Acquisition & Processing
Exact implementation as specified in the assignment
"""

from datasets import load_dataset
import pandas as pd
import numpy as np
import librosa
import soundfile as sf
from pathlib import Path
import logging
import json
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import os
import glob

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_mozilla_common_voice():
    """Load Mozilla Common Voice Bengali dataset as specified"""
    print("📦 Loading Mozilla Common Voice Bengali...")
    
    try:
        # Load dataset as specified in assignment
        cv_dataset = load_dataset("mozilla-foundation/common_voice_11_0", "bn")\
        # cv_dataset = load_dataset("D:\ASSIGNMENTS\MarkopoloAI\data\datasets\common_voice\datasets--mozilla-foundation--common_voice_11_0\snapshots\\audio", "bn")
        
        print("✅ Mozilla Common Voice Bengali loaded successfully!")
        print(f"   • Train samples: {len(cv_dataset['train'])}")
        print(f"   • Test samples: {len(cv_dataset['test'])}")
        print(f"   • Validation samples: {len(cv_dataset['validation'])}")
        
        return cv_dataset
        
    except Exception as e:
        print(f"❌ Error loading Mozilla Common Voice: {e}")
        return None

def load_bengali_ai_dataset():
    """Load Bengali.AI Train Set as specified"""
    print("\n📦 Loading Bengali.AI Train Set...")
    
    try:
        # Load dataset as specified in assignment
        dataset = load_dataset("D:\ASSIGNMENTS\MarkopoloAI\data\datasets\\bengali_ai\\thesven___bengali-ai-train-set-tiny")
        
        print("✅ Bengali.AI Train Set loaded successfully!")
        print(f"   • Train samples: {len(dataset['train'])}")
        
        return dataset
        
    except Exception as e:
        print(f"❌ Error loading Bengali.AI dataset: {e}")
        return None

def analyze_openslr_53_dataset():
    """Analyze the OpenSLR 53 Bengali Dataset (~196K utterances)"""
    print("\n📦 Analyzing OpenSLR 53 Bengali Dataset...")
    
    # Check if asr_bengali directory exists
    openslr_path = Path("asr_bengali")
    if not openslr_path.exists():
        print("❌ OpenSLR 53 dataset not found in asr_bengali directory")
        return None
    
    try:
        # Load metadata file
        metadata_file = openslr_path / "utt_spk_text.tsv"
        if metadata_file.exists():
            df = pd.read_csv(metadata_file, sep='\t', header=None, 
                           names=['utterance_id', 'speaker_id', 'text'])
            
            print("✅ OpenSLR 53 metadata loaded successfully!")
            print(f"   • Total utterances: {len(df):,}")
            print(f"   • Unique speakers: {df['speaker_id'].nunique()}")
            print(f"   • Average text length: {df['text'].str.len().mean():.1f} chars")
            
            # Analyze data directories
            data_dirs = list(openslr_path.glob("data/*"))
            print(f"   • Data directories: {len(data_dirs)}")
            
            # Sample some audio files
            audio_files = []
            for data_dir in data_dirs[:3]:  # Check first 3 directories
                audio_files.extend(list(data_dir.glob("*.flac")))
            
            print(f"   • Sample audio files found: {len(audio_files)}")
            
            return {
                'metadata': df,
                'total_utterances': len(df),
                'unique_speakers': df['speaker_id'].nunique(),
                'data_directories': len(data_dirs),
                'sample_audio_files': len(audio_files)
            }
            
        else:
            print("❌ Metadata file utt_spk_text.tsv not found")
            return None
            
    except Exception as e:
        print(f"❌ Error analyzing OpenSLR 53: {e}")
        return None

def analyze_audio_quality(dataset_info, max_samples=50):
    """Analyze audio quality, duration, and speaker distribution"""
    print("\n🔊 Analyzing Audio Quality and Characteristics...")
    
    results = {
        'durations': [],
        'sample_rates': [],
        'audio_quality_scores': [],
        'speaker_distribution': {},
        'file_formats': {}
    }
    
    # Analyze OpenSLR 53 audio files
    if dataset_info['openslr']:
        print("   📊 Analyzing OpenSLR 53 audio files...")
        
        openslr_path = Path("asr_bengali")
        audio_files = []
        
        # Collect audio files from data directories
        for data_dir in openslr_path.glob("data/*"):
            audio_files.extend(list(data_dir.glob("*.flac"))[:10])  # 10 per directory
        
        # Analyze sample of audio files
        for i, audio_file in enumerate(tqdm(audio_files[:max_samples], desc="Analyzing audio")):
            try:
                # Load audio
                audio, sr = librosa.load(audio_file, sr=None)
                duration = len(audio) / sr
                
                # Basic quality metrics
                rms_energy = np.sqrt(np.mean(audio**2))
                zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(audio))
                
                results['durations'].append(duration)
                results['sample_rates'].append(sr)
                results['audio_quality_scores'].append(rms_energy)
                
                # File format
                file_ext = audio_file.suffix.lower()
                results['file_formats'][file_ext] = results['file_formats'].get(file_ext, 0) + 1
                
            except Exception as e:
                logger.warning(f"Error analyzing {audio_file}: {e}")
                continue
    
    # Calculate statistics
    if results['durations']:
        stats = {
            'total_files_analyzed': len(results['durations']),
            'duration_stats': {
                'mean': np.mean(results['durations']),
                'std': np.std(results['durations']),
                'min': np.min(results['durations']),
                'max': np.max(results['durations'])
            },
            'sample_rate_stats': {
                'most_common': max(set(results['sample_rates']), key=results['sample_rates'].count),
                'unique_rates': list(set(results['sample_rates']))
            },
            'quality_stats': {
                'mean_rms': np.mean(results['audio_quality_scores']),
                'std_rms': np.std(results['audio_quality_scores'])
            },
            'file_formats': results['file_formats']
        }
        
        print(f"   ✅ Analyzed {stats['total_files_analyzed']} audio files")
        print(f"   • Duration: {stats['duration_stats']['mean']:.1f}s ± {stats['duration_stats']['std']:.1f}s")
        print(f"   • Sample Rate: {stats['sample_rate_stats']['most_common']} Hz (most common)")
        print(f"   • RMS Energy: {stats['quality_stats']['mean_rms']:.4f} ± {stats['quality_stats']['std_rms']:.4f}")
        
        return stats
    else:
        print("   ⚠️ No audio files could be analyzed")
        return None

def extract_phonetic_features_for_accent(audio_path, sr=22050):
    """Extract phonetic features for accent detection"""
    try:
        # Load audio
        audio, _ = librosa.load(audio_path, sr=sr)
        
        if len(audio) == 0:
            return None
        
        features = {}
        
        # Key features for BD vs IN accent detection
        
        # 1. MFCC features (crucial for accent)
        mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)
        for i in range(13):
            features[f'mfcc_{i}_mean'] = float(np.mean(mfcc[i]))
            features[f'mfcc_{i}_std'] = float(np.std(mfcc[i]))
        
        # 2. Spectral features
        spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
        features['spectral_centroid_mean'] = float(np.mean(spectral_centroids))
        features['spectral_centroid_std'] = float(np.std(spectral_centroids))
        
        # 3. F0 patterns (key for accent distinction)
        pitches, magnitudes = librosa.piptrack(y=audio, sr=sr)
        pitch_values = pitches[magnitudes > np.percentile(magnitudes, 85)]
        if len(pitch_values) > 0:
            features['f0_mean'] = float(np.mean(pitch_values))
            features['f0_std'] = float(np.std(pitch_values))
        else:
            features['f0_mean'] = 0.0
            features['f0_std'] = 0.0
        
        # 4. Vowel formant approximation
        stft = librosa.stft(audio)
        magnitude = np.abs(stft)
        freqs = librosa.fft_frequencies(sr=sr)
        
        # Approximate formants using spectral peaks
        spectral_peaks = []
        for frame in magnitude.T:
            peaks = np.where(frame > np.percentile(frame, 90))[0]
            if len(peaks) > 0:
                spectral_peaks.extend(freqs[peaks])
        
        if spectral_peaks:
            features['formant_mean'] = float(np.mean(spectral_peaks))
            features['formant_std'] = float(np.std(spectral_peaks))
        else:
            features['formant_mean'] = 0.0
            features['formant_std'] = 0.0
        
        # 5. Rhythmic patterns
        tempo, _ = librosa.beat.beat_track(y=audio, sr=sr)
        features['tempo'] = float(tempo)
        
        # 6. Zero crossing rate (pronunciation patterns)
        zcr = librosa.feature.zero_crossing_rate(audio)[0]
        features['zcr_mean'] = float(np.mean(zcr))
        features['zcr_std'] = float(np.std(zcr))
        
        return features
        
    except Exception as e:
        logger.warning(f"Error extracting features from {audio_path}: {e}")
        return None

def run_task2_complete_pipeline():
    """Run the complete Task 2 pipeline as specified"""
    print("🚀 Task 2: Dataset Acquisition & Processing")
    print("=" * 60)
    
    # Create output directory
    output_dir = Path("outputs/task2_datasets")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    dataset_info = {}
    
    # 1. Load Mozilla Common Voice Bengali (as specified)
    print("\n1️⃣ MOZILLA COMMON VOICE BENGALI")
    print("-" * 40)
    cv_dataset = load_mozilla_common_voice()
    dataset_info['mozilla_cv'] = cv_dataset
    
    # 2. Load Bengali.AI Train Set (as specified)
    print("\n2️⃣ BENGALI.AI TRAIN SET")
    print("-" * 40)
    bengali_ai_dataset = load_bengali_ai_dataset()
    dataset_info['bengali_ai'] = bengali_ai_dataset
    
    # 3. Analyze OpenSLR 53 Bengali Dataset (as specified)
    print("\n3️⃣ OPENSLR 53 BENGALI DATASET")
    print("-" * 40)
    openslr_info = analyze_openslr_53_dataset()
    dataset_info['openslr'] = openslr_info
    
    # 4. Audio Quality Analysis
    print("\n4️⃣ AUDIO QUALITY ANALYSIS")
    print("-" * 40)
    audio_stats = analyze_audio_quality(dataset_info)
    
    # 5. Phonetic Feature Extraction for Accent Detection
    print("\n5️⃣ PHONETIC FEATURE EXTRACTION")
    print("-" * 40)
    accent_features = extract_accent_features_sample(dataset_info)
    
    # 6. Create comprehensive analysis report
    print("\n6️⃣ CREATING ANALYSIS REPORT")
    print("-" * 40)
    
    task2_results = {
        'datasets_loaded': {
            'mozilla_cv': cv_dataset is not None,
            'bengali_ai': bengali_ai_dataset is not None,
            'openslr_53': openslr_info is not None
        },
        'dataset_statistics': {
            'mozilla_cv': get_dataset_stats(cv_dataset) if cv_dataset else None,
            'bengali_ai': get_dataset_stats(bengali_ai_dataset) if bengali_ai_dataset else None,
            'openslr_53': openslr_info
        },
        'audio_quality_analysis': audio_stats,
        'accent_features_sample': accent_features,
        'processing_timestamp': pd.Timestamp.now().isoformat()
    }
    
    # Save results
    results_path = output_dir / "task2_dataset_analysis.json"
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(task2_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ Task 2 results saved to: {results_path}")
    
    # Create visualizations
    create_task2_visualizations(task2_results, output_dir)
    
    # Print summary
    print_task2_summary(task2_results)
    
    return task2_results

def get_dataset_stats(dataset):
    """Get statistics for a HuggingFace dataset"""
    if dataset is None:
        return None
    
    try:
        stats = {}
        for split_name, split_data in dataset.items():
            stats[split_name] = {
                'num_samples': len(split_data),
                'columns': list(split_data.column_names),
                'sample_keys': list(split_data[0].keys()) if len(split_data) > 0 else []
            }
        return stats
    except Exception as e:
        logger.warning(f"Error getting dataset stats: {e}")
        return None

def extract_accent_features_sample(dataset_info, max_samples=20):
    """Extract phonetic features from sample audio files for accent detection"""
    print("   🎵 Extracting phonetic features for accent detection...")
    
    features_list = []
    
    # Process OpenSLR 53 samples
    if dataset_info['openslr']:
        openslr_path = Path("asr_bengali")
        audio_files = []
        
        # Collect sample audio files
        for data_dir in openslr_path.glob("data/*"):
            audio_files.extend(list(data_dir.glob("*.flac"))[:5])  # 5 per directory
        
        print(f"   • Found {len(audio_files)} audio files in OpenSLR 53")
        
        # Extract features from sample files
        for audio_file in tqdm(audio_files[:max_samples], desc="Extracting features"):
            features = extract_phonetic_features_for_accent(audio_file)
            if features:
                features['source'] = 'openslr_53'
                features['file_path'] = str(audio_file)
                features_list.append(features)
    
    print(f"   ✅ Extracted features from {len(features_list)} audio files")
    
    if features_list:
        # Calculate feature statistics
        feature_stats = {}
        feature_names = list(features_list[0].keys())
        
        for feature_name in feature_names:
            if feature_name not in ['source', 'file_path']:
                values = [f[feature_name] for f in features_list if feature_name in f]
                if values:
                    feature_stats[feature_name] = {
                        'mean': float(np.mean(values)),
                        'std': float(np.std(values)),
                        'min': float(np.min(values)),
                        'max': float(np.max(values))
                    }
        
        return {
            'features_extracted': len(features_list),
            'feature_statistics': feature_stats,
            'sample_features': features_list[:5]  # First 5 samples
        }
    else:
        return None

def create_task2_visualizations(results, output_dir):
    """Create visualization plots for Task 2 analysis"""
    print("   📈 Creating visualization plots...")
    
    try:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. Dataset sizes comparison
        ax1 = axes[0, 0]
        dataset_names = []
        dataset_sizes = []
        
        if results['datasets_loaded']['mozilla_cv'] and results['dataset_statistics']['mozilla_cv']:
            cv_stats = results['dataset_statistics']['mozilla_cv']
            total_cv = sum(split['num_samples'] for split in cv_stats.values())
            dataset_names.append('Mozilla CV')
            dataset_sizes.append(total_cv)
        
        if results['datasets_loaded']['bengali_ai'] and results['dataset_statistics']['bengali_ai']:
            ai_stats = results['dataset_statistics']['bengali_ai']
            total_ai = sum(split['num_samples'] for split in ai_stats.values())
            dataset_names.append('Bengali.AI')
            dataset_sizes.append(total_ai)
        
        if results['datasets_loaded']['openslr_53'] and results['dataset_statistics']['openslr_53']:
            openslr_size = results['dataset_statistics']['openslr_53']['total_utterances']
            dataset_names.append('OpenSLR 53')
            dataset_sizes.append(openslr_size)
        
        if dataset_names:
            ax1.bar(dataset_names, dataset_sizes)
            ax1.set_title('Dataset Sizes Comparison')
            ax1.set_ylabel('Number of Samples')
            ax1.tick_params(axis='x', rotation=45)
        
        # 2. Audio duration distribution
        ax2 = axes[0, 1]
        if results['audio_quality_analysis'] and 'duration_stats' in results['audio_quality_analysis']:
            durations = results['audio_quality_analysis'].get('durations', [])
            if durations:
                ax2.hist(durations, bins=20, alpha=0.7)
                ax2.set_title('Audio Duration Distribution')
                ax2.set_xlabel('Duration (seconds)')
                ax2.set_ylabel('Frequency')
        
        # 3. Sample rate distribution
        ax3 = axes[0, 2]
        if results['audio_quality_analysis'] and 'sample_rate_stats' in results['audio_quality_analysis']:
            sr_stats = results['audio_quality_analysis']['sample_rate_stats']
            if 'unique_rates' in sr_stats:
                rates = sr_stats['unique_rates']
                counts = [1] * len(rates)  # Simplified
                ax3.bar([str(r) for r in rates], counts)
                ax3.set_title('Sample Rate Distribution')
                ax3.set_ylabel('Count')
        
        # 4. Feature statistics (if available)
        ax4 = axes[1, 0]
        if results['accent_features_sample'] and 'feature_statistics' in results['accent_features_sample']:
            feature_stats = results['accent_features_sample']['feature_statistics']
            feature_names = list(feature_stats.keys())[:10]  # Top 10
            feature_means = [feature_stats[name]['mean'] for name in feature_names]
            
            ax4.bar(range(len(feature_names)), feature_means)
            ax4.set_title('Top 10 Acoustic Features (Mean)')
            ax4.set_xticks(range(len(feature_names)))
            ax4.set_xticklabels(feature_names, rotation=45, ha='right')
        
        # 5. Dataset loading status
        ax5 = axes[1, 1]
        loading_status = results['datasets_loaded']
        datasets = list(loading_status.keys())
        statuses = [1 if loading_status[d] else 0 for d in datasets]
        colors = ['green' if s else 'red' for s in statuses]
        
        ax5.bar(datasets, statuses, color=colors)
        ax5.set_title('Dataset Loading Status')
        ax5.set_ylabel('Loaded (1=Yes, 0=No)')
        ax5.tick_params(axis='x', rotation=45)
        
        # 6. Summary metrics
        ax6 = axes[1, 2]
        metrics = ['Datasets Loaded', 'Audio Files Analyzed', 'Features Extracted']
        values = [
            sum(results['datasets_loaded'].values()),
            results['audio_quality_analysis']['total_files_analyzed'] if results['audio_quality_analysis'] else 0,
            results['accent_features_sample']['features_extracted'] if results['accent_features_sample'] else 0
        ]
        
        ax6.bar(metrics, values)
        ax6.set_title('Task 2 Summary Metrics')
        ax6.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_dir / 'task2_dataset_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ Visualizations saved to: {output_dir}/task2_dataset_analysis.png")
        
    except Exception as e:
        print(f"   ⚠️ Error creating visualizations: {e}")

def print_task2_summary(results):
    """Print comprehensive Task 2 summary"""
    print("\n" + "="*60)
    print("📋 TASK 2: DATASET ACQUISITION & PROCESSING SUMMARY")
    print("="*60)
    
    # Dataset loading status
    loading_status = results['datasets_loaded']
    print(f"\n📦 Dataset Loading Status:")
    print(f"   • Mozilla Common Voice: {'✅' if loading_status['mozilla_cv'] else '❌'}")
    print(f"   • Bengali.AI Train Set: {'✅' if loading_status['bengali_ai'] else '❌'}")
    print(f"   • OpenSLR 53: {'✅' if loading_status['openslr_53'] else '❌'}")
    
    # Dataset statistics
    if results['dataset_statistics']['openslr_53']:
        openslr_stats = results['dataset_statistics']['openslr_53']
        print(f"\n📊 OpenSLR 53 Statistics:")
        print(f"   • Total Utterances: {openslr_stats['total_utterances']:,}")
        print(f"   • Unique Speakers: {openslr_stats['unique_speakers']}")
        print(f"   • Data Directories: {openslr_stats['data_directories']}")
    
    # Audio quality analysis
    if results['audio_quality_analysis']:
        audio_stats = results['audio_quality_analysis']
        print(f"\n🔊 Audio Quality Analysis:")
        print(f"   • Files Analyzed: {audio_stats['total_files_analyzed']}")
        if 'duration_stats' in audio_stats:
            dur_stats = audio_stats['duration_stats']
            print(f"   • Duration: {dur_stats['mean']:.1f}s ± {dur_stats['std']:.1f}s")
            print(f"   • Range: {dur_stats['min']:.1f}s - {dur_stats['max']:.1f}s")
    
    # Feature extraction
    if results['accent_features_sample']:
        feature_info = results['accent_features_sample']
        print(f"\n🎵 Phonetic Feature Extraction:")
        print(f"   • Features Extracted: {feature_info['features_extracted']} files")
        print(f"   • Feature Types: {len(feature_info['feature_statistics'])} acoustic features")
    
    print(f"\n📁 Output Files:")
    print(f"   • Analysis: outputs/task2_datasets/task2_dataset_analysis.json")
    print(f"   • Plots: outputs/task2_datasets/task2_dataset_analysis.png")
    
    print("\n✅ TASK 2 COMPLETED SUCCESSFULLY!")
    print("📋 Next Steps:")
    print("   1. Proceed to Task 3: Accent Classification Pipeline")
    print("   2. Build BD vs IN Bengali accent classifier")
    print("   3. Filter Bangladeshi accent samples")

def main():
    """Main function for Task 2"""
    results = run_task2_complete_pipeline()
    
    if results:
        print(f"\n🎯 Task 2 Complete!")
        datasets_loaded = sum(results['datasets_loaded'].values())
        print(f"📊 Datasets Loaded: {datasets_loaded}/3")
        print(f"📁 Results saved to outputs/task2_datasets/")
        return True
    else:
        print("❌ Task 2 failed. Check error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
