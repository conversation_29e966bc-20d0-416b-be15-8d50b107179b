#!/usr/bin/env python3
"""
Test audio playback to verify files are working
"""

import soundfile as sf
import numpy as np
from pathlib import Path

def test_audio_file(filename):
    """Test if an audio file is valid and playable"""
    print(f"\n🔍 Testing: {filename}")
    print("-" * 40)
    
    if not Path(filename).exists():
        print(f"❌ File not found: {filename}")
        return False
    
    try:
        # Read audio file
        audio, sample_rate = sf.read(filename)
        
        print(f"✅ File loaded successfully")
        print(f"📊 Sample rate: {sample_rate} Hz")
        print(f"📊 Duration: {len(audio) / sample_rate:.2f} seconds")
        print(f"📊 Samples: {len(audio)}")
        print(f"📊 Audio range: [{np.min(audio):.3f}, {np.max(audio):.3f}]")
        print(f"📊 RMS level: {np.sqrt(np.mean(audio**2)):.3f}")
        
        # Check if audio has content (not just silence)
        if np.max(np.abs(audio)) > 0.001:
            print(f"✅ Audio has content (not silent)")
        else:
            print(f"⚠️ Audio appears to be silent or very quiet")
        
        # Check for clipping
        if np.max(np.abs(audio)) > 0.99:
            print(f"⚠️ Audio may be clipped")
        else:
            print(f"✅ Audio levels are good")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def create_test_audio():
    """Create a simple test audio file"""
    print(f"\n🔧 Creating test audio file...")
    
    # Generate a simple test tone
    duration = 2.0  # seconds
    sample_rate = 22050
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Create a pleasant test tone (A4 = 440 Hz)
    frequency = 440
    audio = 0.3 * np.sin(2 * np.pi * frequency * t)
    
    # Add some harmonics
    audio += 0.1 * np.sin(2 * np.pi * frequency * 2 * t)
    audio += 0.05 * np.sin(2 * np.pi * frequency * 3 * t)
    
    # Apply envelope
    envelope = np.exp(-t * 0.5) * (1 - np.exp(-t * 5))
    audio *= envelope
    
    # Save test audio
    filename = "test_tone.wav"
    sf.write(filename, audio, sample_rate)
    
    print(f"✅ Test audio created: {filename}")
    return filename

def main():
    """Test all audio files"""
    print("🔍 AUDIO FILE TESTING")
    print("=" * 50)
    
    # Test files to check
    test_files = [
        "debug_test_audio.wav",
        "debug_griffinlim_test.wav",
        "bd_tts_test_1.wav",
        "bd_tts_test_2.wav",
        "bd_tts_test_3.wav",
        "bd_tts_test_4.wav"
    ]
    
    results = {}
    
    for filename in test_files:
        results[filename] = test_audio_file(filename)
    
    # Create a reference test tone
    test_tone = create_test_audio()
    results[test_tone] = test_audio_file(test_tone)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 AUDIO TEST SUMMARY")
    print("=" * 50)
    
    working_files = []
    broken_files = []
    
    for filename, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {filename}")
        
        if success:
            working_files.append(filename)
        else:
            broken_files.append(filename)
    
    print(f"\n📊 Results:")
    print(f"   • Working files: {len(working_files)}")
    print(f"   • Broken files: {len(broken_files)}")
    
    if working_files:
        print(f"\n🎵 Working audio files:")
        for filename in working_files:
            print(f"   • {filename}")
        print(f"\n🔊 Try playing these files in your media player!")
    
    if broken_files:
        print(f"\n❌ Broken audio files:")
        for filename in broken_files:
            print(f"   • {filename}")

if __name__ == "__main__":
    main()
